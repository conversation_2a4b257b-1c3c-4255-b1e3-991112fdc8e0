<template>
  <PinkBackPage>
    <template #toplink>
      <div>
        <span class="adminLink">{{ $t( 'onboarding.getSupport' ) }}</span>
        <span class="adminLink">{{ $t( 'lang.english' ) }}</span>
      </div>
    </template>
    <template #cntContent>
      <div class="flex items-center w-full justify-center ">
        <div class="formCont">
          <div class="headerRow">
            <div class="headerlogin">
              {{ $t( 'loginForm.loginToyourAcc' ) }}
            </div>
            <div class="signupSwitch">
              signUp
            </div>
          </div>
          <VForm class="formExact mt-13" @submit.prevent="handleLogin">
            <div>
              <div class="lbl">
                Email*
              </div>
              <input
                class="ftextfield"
                v-model="credentials.user"
                placeholder="Enter your email"
                label="Password"
                autocomplete="username"
              />
            </div>
            <div>
              <div class="lbl">
                Password*
              </div>
            <input
              class="ftextfield"
              v-model="credentials.password"
              type="password"
              placeholder="Enter password"
              autocomplete="current-password"
            />
            </div>
            <div class="forgot">
              Forgot password?
            </div>
            <div>
            <v-btn class="loginBtn" @click="handleLogin" color="#7E00FF">
              {{ $t('login.title') }}
            </v-btn>
          </div>
          </VForm>
          <div class="assistStr">
            <Assistance />
          </div>
        </div>
      </div>
    </template>
  </PinkBackPage>
</template>
<script setup lang="ts">
  import { PinkBackPage } from '@ui/pages/common';
  import { useAuthStore } from '@stores/auth.ts';
  import { ref } from 'vue'
  import { Assistance } from '@ui/components/common'
  const authStore = useAuthStore();
  const credentials = ref({ user: '', password: '' });
  
  const handleLogin = async () => {
    await authStore.login(credentials.value);
  };
</script>
<style scoped>
.adminLink{
  cursor:pointer;
  color: #fff;
  font-size: 14px;
  text-decoration:none;
  margin:10px;
}
.adminLink:hover{
  text-decoration:underline;
}
.imgback{
  border-style: none;
  z-index: -1;
}
.formCont{
  background: #fafafa;
  width: 600px;
  height: 100%;
  border-radius:15px;
  color:#999;
  padding:40px;
}
.headerRow{
  display: flex;
  justify-content: space-between; /* Distributes items to the far left and right */
  align-items: center; /* Optional: Align items vertically */
  width: 100%; /* Adjust the container width as needed */
  padding: 10px; /* Optional: Adds spacing */
}
.headerlogin{
  color: #212121;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 36px; 
  margin-left:20px;
  }
.signupSwitch{
  color: #7E00FF;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  padding: 2px 10px;
  cursor:poiter;
  line-height:27px;
  background:#7E00FF0D;
  border: #7E00FF solid 1px;
  border-radius:15px;
  text-align:center;
  }
  .ftextfield{
    border:none;
    border-radius:30px;
    color:#000;
    background:#fff;
    padding:5px 10px;
    height:40px;
    width:100%; 
    margin-top:10px;
  }
  .lbl{
    color: #212121;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 128.571% */
    margin-top:20px;
  }
  .formExact{
    padding-left:25px;
    padding-right:25px;
  }

  .loginBtn{
    border:none;
    border-radius:15px;
    background:#7E00FF;
    padding:5px 10px;
    height:60px;
    width:100%; 
    margin-top:30px;
  }

  .forgot{
    margin-top:20px;
    color:#212121;
    font-size:14px;
    text-decoration:underline;
    float:right;
    cursor:pointer;
  }

  .assistStr{
    color:#212121;
    margin-top:80px;
    font-size:14px;
    width:100%;
  }
</style>