<template>
  <AsteriskPage className="asteriskBack asterisk-back">
    <template #toplink>
      <span @click="showChoseRoleAction" class="adminLink">{{ $t( 'onboarding.back' ) }}</span>
    </template>
    <template #cntContent>
      <div class="delimiter">
        <SlidesDelimiter
          :currentIndex="currentIndex"
          :slides="slides"
        />
      </div>
      <div class="animated" v-for="(slide, i) in slides" :key="i" v-show="i === currentIndex">
        <div class="titleText">
          {{ slide.title }}
        </div>
        <div class="normalText">
          {{ slide.text }}
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex items-center w-full justify-center">
      <ShowLoginButton
        @click="showLoginAction"
      />
    </div>
    </template>
  </AsteriskPage>
</template>
<script setup lang="ts">
  import { SlidesDelimiter } from '@ui/components/common';
  import { ShowLoginButton } from '@ui/components/common/unauth';
  import { AsteriskPage } from '@ui/pages/common';
  import { useAppStore } from '@stores/app.ts'
  import { PAGES } from '@constants/core';

  const { currentIndex = 0, slides = [] } = defineProps({
    currentIndex: Number,
    slides: Array
  })
  
  const appStore = useAppStore();
  const showLoginAction = () => {
    appStore.setPage(PAGES.LOGINFORM);
  };
  const showChoseRoleAction = () => {
    appStore.setPage(PAGES.CHOSEROLE);
  };
</script>
<style scoped>
.adminLink{
  cursor:pointer;
  color: #C9F;
  font-size: 14px;
  text-decoration:underline;
  text-transform:uppercase;
}
.delimiter{
  margin-top:350px;
}
.titleText{
  margin-top:30px;
  color: var(--White, #FFF);
  font-feature-settings: 'liga' off, 'clig' off;
  font-size: 60px;
  font-style: normal;
  font-weight: 500;
  width:500px;
  line-height: 70px; /* 125% */
}
.normalText{
  color: #FFF;
  font-feature-settings: 'liga' off, 'clig' off;
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 30px; /* 187.5% */
}
.animated{
  transition: all 0.9s ease;
}
</style>