<template>
  <PageRcont leftClassName="pinkback">
    <template #left>
      <div class="image-container">
      <transition name="fade">
        <img  :src="imgUrl" :key="currentIndex" alt="Slideshow Image"  />
      </transition>
    </div>
    </template>
    <template #right>
      <CarouselRight
        :currentIndex="currentIndex"
        :slides="slides"
      />
    </template>
  </PageRcont>
</template>
<script setup lang="ts">
  import { computed, onMounted, ref, onBeforeUnmount } from 'vue';
  import { CONFIGS } from '@constants/core/configs';
  import { PageRcont } from '@ui/pages/common';
  import { CarouselRight } from '@ui/pages/desktop/unauth';

  const slides = [
    { title: 'All Web3 Campaigns in One Place', text: 'Compare offers, pick what aligns with your content, and get paid in tokens — fast, easy, onchain.'},
    { title: 'Onchain Protection. Auto Payments.', text: 'Funds are locked in a smart contract and released the moment your deliverables are approved.'},
    { title: 'Campaign Control. Onchain Reputation.', text: 'Your personal dashboard for everything, manage campaigns, track payouts, and analyze performance.'},
  ]

  const images = import.meta.glob('../../../../assets/images/carousel/*.svg', { eager: true });

  let interval = ref(0);
  const currentIndex = ref(0);

  const imgUrl = computed(() => { 
    return images[`../../../../assets/images/carousel/car${currentIndex.value}.svg`]?.default || '';
  });

  const goNext = () => {
    
    if (currentIndex.value < slides.length - 1) {
      currentIndex.value++;
    } else {
      currentIndex.value = 0;
    }
  }

  onMounted(() => {
    interval = setInterval( goNext , CONFIGS.ONBOARDING.CAROUSEL_TIMEOUT);
  });
  onBeforeUnmount(() => {
    clearInterval(interval);
  }) 
  

</script>
<style scoped>

.image-container {
  display:flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
img {
  position: absolute;
  top: 20%;
  left: 0;
  width: 70%;
  height: 70%;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>