<template>
  <Page>
    <template #main>
      <div>
        <div class="imgback2">
          <img src="../../../../assets/svg/bigPinkGradient.svg" />
        </div>
        <div class="h-full formCont">
          <div class="headerlogin">
            Log in to your Account
          </div>
          <div class="signupSwitch">
            signUp
          </div>
          <VForm class="formExact mt-13" @submit.prevent="handleLogin">
            <div>
              <div class="lbl">
                Email*
              </div>
              <input
                class="ftextfield"
                v-model="credentials.user"
                placeholder="Enter your email"
                label="Password"
                autocomplete="username"
              />
            </div>
            <div>
              <div class="lbl">
                Password*
              </div>
            <input
              class="ftextfield"
              v-model="credentials.password"
              type="password"
              placeholder="Enter password"
              autocomplete="current-password"
            />
            </div>
            <div class="forgot">
              Forgot password?
            </div>
            <div>
            <v-btn class="loginBtn" @click="handleLogin" color="#7E00FF">
              {{ $t('login.title') }}
            </v-btn>
          </div>
          </VForm>
          <div class="assistStr">
            <Assistance />
          </div>
        </div>
      </div>
    </template>
  </Page>
</template>
<script setup lang="ts">
  import { Page } from '@ui/pages/common';
  import { useAuthStore } from '@stores/auth.ts';
  import { ref } from 'vue'
  import { Assistance } from '@ui/components/common'
  const authStore = useAuthStore();
  const credentials = ref({ user: '', password: '' });
  
  const handleLogin = async () => {
    await authStore.login(credentials.value);
  };
</script>
<style scoped>
.imgback{
  border-style: none;
  z-index: -1;
}
.formCont{
  background: #fafafa;
    position: absolute;
    top: 40px;
    left: 0px;
    width: 100%;
    height: 100%;
    border-radius:15px;
    color:#999;
}
.headerlogin{
  color: #212121;
  font-size: 24px;
  font-style: normal;
  font-weight: 700;
  line-height: 36px; 
  margin-top:40px;
  margin-left:20px;
  width:200px;
  }
.signupSwitch{
  color: #7E00FF;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  margin-top:40px;
  margin-right:20px;
  width:70px;
  position: absolute;
    top: 0px;
    right: 0px;
    cursor:poiter;
    height:30px;
    line-height:27px;
    border: #7E00FF solid 1px;
    border-radius:15px;
    text-align:center;
  }
  .ftextfield{
    border:none;
    border-radius:30px;
    color:#000;
    background:#fff;
    padding:5px 10px;
    height:40px;
    width:100%; 
    margin-top:10px;
  }
  .lbl{
    color: #212121;
    font-feature-settings: 'liga' off, 'clig' off;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px; /* 128.571% */
    margin-top:20px;
  }
  .formExact{
    padding-left:25px;
    padding-right:25px;
  }

  .loginBtn{
    border:none;
    border-radius:15px;
    background:#7E00FF;
    padding:5px 10px;
    height:60px;
    width:100%; 
    margin-top:30px;
  }

  .forgot{
    margin-top:20px;
    color:#999;
    font-size:14px;
    text-decoration:underline;
    float:right;
    cursor:pointer;
  }
  .assistStr{
    color:#999;
    bottom:80px;
    font-size:14px;
    position:absolute;
    width:100%;
  }
</style>