<template>
  <Page>
    <template #main>
      <div>
        <v-carousel
          height="800"
          :continuous="true"
          :show-arrows="false"
          :cycle="true"
          hide-delimiters
          hide-delimiter-background
        >
          <v-carousel-item
            v-for="(slide, i) in slides"
            :key="i"
          >
            <div class="imgback">
              <img v-if="slide.back == 1" src="../../../../assets/images/carousel/car0.svg" />
              <img v-if="slide.back == 2" src="../../../../assets/images/carousel/car1.svg" />
              <img v-if="slide.back == 3" src="../../../../assets/images/carousel/car2.svg" />
            </div>
            <div class="h-full">
                <Logo class="logoInCar" width="46" height="42" />
                <div class="bottomPart">
                  <div class="textPart d-flex flex-col fill-height justify-center align-end">
                    <div class="stitle">{{ slide.title }}</div>
                    <div class="stext">{{ slide.text }}</div>
                  </div>
                </div>
            </div>
          </v-carousel-item>
        </v-carousel>
      </div>
    </template>
    <template #footer>
      <ShowLoginButton
        @click="showLoginAction"
      />
    </template>
  </Page>
</template>
<script setup lang="ts">
  import { Page } from '@ui/pages/common';
  import { Logo } from '@ui/components/graphic';
  import { ShowLoginButton } from '@ui/components/common/unauth';
  import { useAppStore } from '@stores/app.ts';
  import { PAGES } from '@constants/core';

  const appStore = useAppStore();

  const slides = [
    {back:1, title: 'All Web3 Campaigns in One Place', text: 'Compare offers, pick what aligns with your content, and get paid in tokens — fast, easy, onchain.'},
    {back:2,title: 'Onchain Protection. Auto Payments.', text: 'Funds are locked in a smart contract and released the moment your deliverables are approved.'},
    {back:3,title: 'Campaign Control. Onchain Reputation.', text: 'Your personal dashboard for everything, manage campaigns, track payouts, and analyze performance.'},
  ]
  const showLoginAction = () => {
    appStore.setPage(PAGES.LOGINFORM);
  };
</script>
<style scoped>
.imgback{
  border-style: none;
  position: absolute;
  display:flex;
  align-items: center;
  justify-content: center;
  height:100%;
  z-index: -1;
}
.logoInCar{
  margin-top:40px;
  margin-left:25px;
}
.bottomPart{
  position:absolute;
  bottom:0px;
  background: linear-gradient(180deg, rgba(10, 12, 46, 0.00) 0%, #0B0624 31.85%);
  height:310px;
}
.textPart{
  margin-left:25px;
}
.stitle{
  color: #FFF;
  padding-right: 70px;
  font-feature-settings: 'liga' off, 'clig' off;
  font-size: 36px;
  font-style: normal;
  font-weight: 500;
  line-height: 46px; /* 127.778% */
}
.stext{
color: #999;
padding-right: 70px;
font-size: 14px;
font-style: normal;
line-height: 20px; 
}
</style>