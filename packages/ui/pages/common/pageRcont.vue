<template>
  <v-app>
    <v-main>
      <div class="flex flex-row h-full">
        <div :class="['leftColumn', leftClassName]" ><slot name="left"></slot></div>
        <div class="rightColumn"><slot name="right"></slot></div>
      </div>
    </v-main>
  </v-app>    
</template>
<script setup lang="ts">
  defineProps({
    leftClassName: String
});
</script>
<style scoped>
  .leftColumn {
    flex: 1; 
    background-size: cover; 
    background-repeat: no-repeat; 
    background-position: center; 
  }
  .rightColumn{
    width:675px;
  }
</style>>