<template>
  <Page className="pinkback rootBack">
    <template #main>
      <div  class="flex flex-col items-center justify-top h-full">
        <div class="cnt_container w-full">
          <div class="flex flex-row justify-between">
            <Logo width="46" height="42" />
            <slot name="toplink"></slot>
          </div>
          <slot name="cntContent"></slot>
        </div>
      </div>
    </template>
    <template #footer>
      <slot name="footer"></slot>
    </template>
  </Page>
</template>
<script setup lang="ts">
  import { Page } from '@ui/pages/common';
  import { Logo } from '@ui/components/graphic';
</script>
<style scoped>
.rootBack{
  background-repeat:no-repeat;
  background-size: cover;
}
</style>