<template>
  <AsteriskPage className="asteriskBack asterisk-back">
    <template #toplink>
      <a href="#" class="adminLink">{{ $t( 'onboarding.imadmin' ) }}</a>
    </template>
    <template #cntContent>
      <div class="welcomeText">
        {{ $t( 'onboarding.wellcome.main' ) }}
      </div>
      <div :class="[choseText]">
        {{ $t( 'onboarding.wellcome.choseRole' ) }}
      </div>
      <div :class="['flex mt-10', btnWrapper]">
        <AsteriskCard
          class="btn gradient-purple"
          @click="setInfluencer" 
          :title=" $t('onboarding.wellcome.button.inf.title') "
          :text=" $t('onboarding.wellcome.button.inf.text') "
          :mode="props.mode"
        >
          <template #icon><div class="icon influencer-icon"></div></template>
        </AsteriskCard>
        <AsteriskCard
          :class="['btn gradient-blue', imaCompany]"
          @click="setProject"  
          :title=" $t('onboarding.wellcome.button.comp.title') "
          :text=" $t('onboarding.wellcome.button.comp.text') "
          :mode="props.mode"
        >
          <template #icon><div class="icon company-icon"></div></template>
        </AsteriskCard>
      </div>
    </template>
    <template #footer>
      <Assistance class="text-white" />
    </template>
  </AsteriskPage>
</template>
<script setup lang="ts">
  import {computed} from "vue";
  import { AsteriskPage } from '@ui/pages/common';
  import { AsteriskCard } from '@ui/components/common';
  import { useUserStore } from '@stores/user.ts';
  import { useAppStore } from '@stores/app.ts'
  import { PAGES } from '@constants/core';
  import { Assistance } from '@ui/components/common'
  import { DEVICE } from '@constants/core';

  const userStore = useUserStore();
  const appStore = useAppStore();

  const setInfluencer = () => {
    userStore.setInfluencer();
    appStore.setPage(PAGES.CAROUSEL);
  };
  const setProject = () => {
    userStore.setProject();
    appStore.setPage(PAGES.CAROUSEL);
  };

  const props = defineProps({
    mode: Boolean
  });

  const btnWrapper = computed(() => (props.mode == DEVICE.DESKTOP ? 'flex-row' : 'flex-col'));
  const imaCompany = computed(() => (props.mode == DEVICE.DESKTOP ? 'ml-3' : 'mt-4'));
  const choseText = computed(() => (props.mode == DEVICE.DESKTOP ? 'choseTextDesctop' : 'choseTextMobile'));
</script>
<style scoped>
.icon{
  width:27px;
  height:27px;
}
.adminLink{
  color: #C9F;
  font-size: 14px;
  text-decoration:underline;
}
.welcomeText{
  font-size:16px;
  margin-top:50px;
  font-weight: 400;
}
.choseTextMobile{
  font-size:36px;
  line-height:36px;
  margin-top:20px;
  font-weight: 700;
}
.choseTextDesctop{
  font-size:64px;
  line-height:64px;
  margin-top:20px;
  font-weight: 500;
  width:500px
}
.custom-btn {
  display: flex;
  overflow:hidden;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 0;
  border-radius:15px;
}

</style>