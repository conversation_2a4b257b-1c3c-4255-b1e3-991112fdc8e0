<template>
  <Page>
    <template #main>
      Wellcome to dashboard!
      <template v-show="false">
        <p>Count: {{ count }}</p>
        <CommonButton @click="increment">Increment</CommonButton>
      </template>
      <LogOutButton />
    </template>
  </Page> 
</template>
<script  setup lang="ts">
  import { Page } from '@ui/pages/common';
  import { LogOutButton } from '@ui/components/common'
  import { CommonButton } from '@ui/components/common'
  import { useCounter } from '@core'
  const { count, increment } = useCounter()
</script>