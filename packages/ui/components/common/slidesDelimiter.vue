<template>
  <div class="flex w-full justify-start">
    <div :class="['slideIcon', { 'active': i === currentIndex }]" v-for="(slide, i) in slides" :key="i"></div>
  </div>
</template>
<script setup lang="ts">
  const { currentIndex = 0, slides = [] } = defineProps({
    currentIndex: Number,
    slides: Array
  })
</script>
<style scoped>
  .slideIcon{
    background:#999;
    width:10px;
    height: 7px;
    border-radius:7px;
    margin:3px;
    transition: all 0.5s ease;
  }
  .active{
    background:#fff;
    width: 20px;
  }
</style>