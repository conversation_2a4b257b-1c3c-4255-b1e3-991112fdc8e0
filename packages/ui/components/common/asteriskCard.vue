<template>
  <div>
    <div class="btn-cont">
      <div class="text-col">
        <slot name="icon"></slot>
        <div :class="['btn-title', btnTitle]">
          {{ title }}
        </div>
        <div :class="['btn-text', btnText]">
          {{ text}}
        </div>
      </div>
      <Asterisk width="30" height="20"/>
    </div>   
  </div>  
</template>

<script setup lang="ts">
  import {computed} from "vue";
  import { DEVICE } from '@constants/core';
  import { Asterisk } from '@ui/components/graphic';
  const { text, title, mode } = defineProps({
    title: String,
    text: String,
    mode: DEVICE
  });

  const btnText = computed(() => (mode == DEVICE.DESKTOP ? 'btnText-desctop' : 'btnText-mobile'));
  const btnTitle = computed(() => (mode == DEVICE.DESKTOP ? 'btnTitle-desctop' : 'btnTitle-mobile'));
</script>

<style scoped>
.btn-cont{
  margin:25px;
  display:flex;
  flex-direction: row;
  justify-content: space-between;
}
.text-col{
  display:flex;
  flex-direction: column;
}
.btn-title{
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
}
.btn-text{
  margin-top:15px;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
}
.btnText-mobile{
  font-size: 12px;
}
.btnText-desctop{
  font-size: 16px;
  line-height:25px;
  width: 225px;
  margin-top:60px;
  margin-bottom:20px;
}
.btnTitle-mobile{
  font-size: 16px;
  margin-top: 10px;
}
.btnTitle-desctop{
  font-size: 24px;
  width: 160px;
  line-height:34px;
  margin-top: 20px;
}
</style>