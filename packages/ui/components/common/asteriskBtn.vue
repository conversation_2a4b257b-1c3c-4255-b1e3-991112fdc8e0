<template>
  <div class="astBtn">
    <div class="btn-cont">
      <div class="text-col">
          {{ text }}
      </div>
      <Asterisk class="asterisk" color="#000" width="20" height="14"/>
    </div>   
  </div>
</template>

<script setup lang="ts">
  import { Asterisk } from '@ui/components/graphic';

 const { text } = defineProps({
    text: String
  });
</script>

<style scoped>
.astBtn{
  background:#fff;
  color:#000;
  border-radius:15px;
  cursor:pointer;
  width:350px;
}
.btn-cont{
  padding:20px;
  display:flex;
  flex-direction: row;
  justify-content: space-between;
}
.text-col{
  display:flex;
  align-items: center;
  justify-content: center;
  width:100%;
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  line-height: 20px;
}
.asterisk{
  margin-top:3px;
}
</style>