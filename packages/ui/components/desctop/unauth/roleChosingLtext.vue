<template>
  <Page>
    <template #main>
      <div  class="flex flex-col items-center justify-center h-full">
          <div class="gatewayto">
            {{ $t( 'onboarding.wellcome.gatewayTo' ) }}
          </div>         
          <div class="largeText">
            {{ $t( 'onboarding.wellcome.creatorXcryptoEconomy' ) }}
          </div>
      </div>
    </template>
    <template #footer>
      <div class="footerText">
        {{ $t( 'company.nameDomain' ) }}
      </div>
    </template>
  </Page>
</template>
<script setup lang="ts">
  import { Page } from '@ui/pages/common';
</script>
<style scoped>
.footerText{
  color:#fff;
  text-align:left;
  font-size:16px;
  padding-left:50px;
}
.gatewayto{
  color: #FFDF4E;
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: Inter;
  font-size: 30px;
  font-style: normal;
  font-weight: 500;
  line-height: 118px; /* 491.667% */
  letter-spacing: 7px;
  text-transform: uppercase;
}
.largeText{
  color: #E9DAFF;
text-align: center;
font-feature-settings: 'liga' off, 'clig' off;
font-family: "Space Grotesk";
font-size: 48px;
font-style: normal;
font-weight: 600;
line-height: 59px; /* 122.917% */
width:500px;
}
.v-application{
  background:none!important;
}

.v-footer{
  align-items: start!important;
  justify-content: left!important;
}
</style>