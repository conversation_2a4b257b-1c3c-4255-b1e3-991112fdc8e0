<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="width" :height="height" viewBox="0 0 20 14" fill="none">
    <path d="M19.736 7.26209C19.8381 7.1725 19.8793 7.0324 19.8418 6.90217C19.8037 6.77143 19.6938 6.67507 19.56 6.65475L15.4274 5.73282C14.5279 5.53214 13.8176 4.84254 13.5905 3.94928L12.7331 0.576942C12.733 0.577104 12.7331 0.576721 12.7331 0.576942C12.7333 0.576781 12.7335 0.575928 12.7335 0.575707C12.6938 0.420678 12.5542 0.312495 12.395 0.3125C12.2346 0.312497 12.0955 0.42135 12.0564 0.576053L11.2422 3.78051C10.9934 4.75979 10.1678 5.48508 9.16461 5.60562L0.440377 6.65391C0.270053 6.68047 0.144526 6.82684 0.144533 6.99872C0.144014 7.17113 0.269555 7.3175 0.439869 7.34406L9.16439 8.39266C10.1674 8.51322 10.993 9.23839 11.2419 10.2175L12.0567 13.4228C12.0962 13.577 12.2358 13.6853 12.3952 13.6854C12.5551 13.6848 12.6942 13.577 12.7333 13.4223L13.5906 10.0499C13.8177 9.15651 14.5281 8.46685 15.4278 8.26622L19.5595 7.34492C19.6251 7.3345 19.686 7.30585 19.736 7.26209Z" :fill="color" fill-opacity="0.95"/>
  </svg>
</template>
<script setup>
const { width, height, color = "#fff" } = defineProps({
    width: String,
    height: String,
    color: String
  });

</script>