<template>
  <svg xmlns="http://www.w3.org/2000/svg" :width="width" :height="height" viewBox="0 0 103 95" fill="none">
    <path d="M14.7533 30.1153C19.4906 30.1154 23.0889 31.4671 25.5476 34.1704C28.0062 36.8139 29.2359 40.6593 29.2359 45.7056V47.9579C29.2359 51.3822 28.6659 54.1463 27.5265 56.249C26.3871 58.2914 24.6177 59.7633 22.219 60.6645V60.8452C24.7975 61.8065 26.6577 63.3691 27.7971 65.5318C28.9362 67.6944 29.5065 70.4581 29.5065 73.8217V79.4097C29.5064 84.4559 28.2467 88.3318 25.728 91.0352C23.2693 93.6783 19.6105 94.9998 14.7533 95C9.89602 95 6.20715 93.6478 3.68832 90.9448C1.22951 88.2414 8.0607e-05 84.3657 0 79.3193V74.1832H9.35554V79.77C9.3557 83.9149 11.0645 85.9882 14.4827 85.9883C16.1614 85.9882 17.4216 85.4776 18.2612 84.4566C19.1607 83.3752 19.6108 81.5118 19.6109 78.8686V73.7325C19.6109 70.8492 19.1 68.8062 18.0808 67.6045C17.0614 66.3431 15.4125 65.7116 13.1341 65.7114H9.80544V56.6997H13.4036C15.3822 56.6996 16.8518 56.1891 17.8113 55.168C18.8307 54.1467 19.3402 52.4336 19.3403 50.0307V45.4345C19.3402 43.2724 18.8908 41.68 17.9917 40.6587C17.0922 39.6374 15.8915 39.1271 14.3924 39.127C11.2142 39.1272 9.62503 41.08 9.62503 44.9849V49.1293H0.269497V45.7959C0.269497 40.7494 1.49896 36.874 3.95781 34.1704C6.47667 31.4669 10.0754 30.1153 14.7533 30.1153Z" fill="#FFF7EA"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M51.4014 30.1153C56.199 30.1154 59.8574 31.4973 62.3762 34.2608C64.8946 36.9643 66.1547 40.8694 66.1547 45.9756V47.1469H56.7992V45.3452C56.7992 41.2 55.0891 39.1272 51.6709 39.127C49.8119 39.127 48.4323 39.698 47.5327 40.8394C46.6334 41.981 46.1841 43.9936 46.1841 46.8769V58.9532H46.3634C48.1026 55.2885 51.1013 53.4556 55.3593 53.4556C58.9573 53.4557 61.6864 54.6874 63.5455 57.1504C65.4044 59.6136 66.3351 63.1887 66.3351 67.8745V79.1386C66.335 84.1849 65.0451 88.091 62.4664 90.8545C59.8876 93.618 56.1688 94.9999 51.3112 95C46.4538 95 42.7349 93.6177 40.1561 90.8545C37.5774 88.091 36.2886 84.1849 36.2885 79.1386V46.5166C36.2885 35.5823 41.326 30.1153 51.4014 30.1153ZM51.3112 62.4673C47.8929 62.4673 46.1841 64.5402 46.1841 68.6855V79.8604C46.1844 83.9451 47.8931 85.9882 51.3112 85.9883C54.7291 85.9881 56.4392 83.945 56.4395 79.8604V68.6855C56.4395 64.5403 54.7294 62.4675 51.3112 62.4673Z" fill="#FFF7EA"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M87.0607 30.1153C91.9181 30.1154 95.6361 31.4974 98.2148 34.2608C100.793 37.0243 102.083 40.9293 102.083 45.9756V79.1386C102.083 84.185 100.794 88.091 98.2148 90.8545C95.6361 93.6177 91.9179 94.9998 87.0607 95C82.2033 95 78.4844 93.6177 75.9056 90.8545C73.3268 88.091 72.0369 84.185 72.0369 79.1386V45.9756C72.0369 40.9291 73.3269 37.0244 75.9056 34.2608C78.4844 31.4972 82.2029 30.1153 87.0607 30.1153ZM87.0875 47.0644C86.9416 46.8966 86.7135 46.8288 86.5017 46.8903C86.2889 46.9529 86.1317 47.1341 86.0986 47.3544L84.4025 61.0114C84.1989 62.6505 83.0237 63.9967 81.4403 64.4039L76.204 65.7504L76.2029 65.7493C75.9506 65.8148 75.773 66.045 75.7731 66.3071C75.7733 66.571 75.9512 66.7995 76.2029 66.8638L81.6563 68.265C83.1297 68.6436 84.2606 69.8398 84.5684 71.3451L86.0986 78.8296C86.1422 79.1095 86.3803 79.316 86.6598 79.316C86.9402 79.3165 87.1778 79.1096 87.2211 78.8296L88.7512 71.3462C89.0589 69.8407 90.1898 68.6448 91.6633 68.2661L97.1179 66.8638C97.3685 66.7986 97.5452 66.5692 97.5455 66.3071C97.5446 66.044 97.3684 65.8149 97.1167 65.7504L91.8805 64.4039C90.2969 63.9968 89.1219 62.6507 88.9183 61.0114L87.2222 47.3555C87.2053 47.2476 87.1586 47.1466 87.0875 47.0644Z" fill="#FFF7EA"/>
    <path d="M39.0781 13.0234C39.0781 14.9749 39.9171 15.9507 41.5949 15.9507C42.5805 15.9505 43.3675 15.6004 43.9558 14.8998C44.544 14.1909 44.8389 13.2621 44.8389 12.1153V5.29346H47.8679V18.2577H45.232L44.8389 15.8793H44.6239C44.2424 16.7074 43.6858 17.3528 42.9546 17.8148C42.2232 18.2688 41.356 18.4963 40.3543 18.4964C38.9787 18.4964 37.9125 18.0542 37.1571 17.17C36.4019 16.2859 36.0246 15.0511 36.0246 13.4662V5.29346H39.0781V13.0234Z" fill="#FFF7EA"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M55.8905 5.04245C57.727 5.04254 59.1707 5.58037 60.2202 6.65559C61.2933 7.70688 61.8304 9.12875 61.8305 10.9205C61.8305 11.3824 61.7865 11.8968 61.6991 12.4622H52.6699C52.6619 13.6331 52.9406 14.537 53.5051 15.1742C54.0775 15.8114 54.8803 16.1302 55.9138 16.1303C56.6691 16.1302 57.3176 15.9552 57.8582 15.6048C58.3987 15.2544 58.745 14.8001 58.8961 14.2427H61.9018C61.6871 15.501 61.0267 16.5286 59.9218 17.3251C58.8564 18.1056 57.5284 18.4963 55.9383 18.4964C54.038 18.4964 52.5153 17.8866 51.3703 16.668C50.2173 15.4413 49.6397 13.8161 49.6397 11.7929C49.6398 9.74614 50.209 8.11723 51.3458 6.9066C52.4908 5.66418 54.0061 5.04245 55.8905 5.04245ZM55.8192 7.39634C54.9049 7.39634 54.1643 7.67134 53.5997 8.22076C53.0356 8.76223 52.7331 9.48319 52.6932 10.3828H58.8003C58.7843 9.43541 58.5141 8.70267 57.9896 8.18506C57.4729 7.65952 56.7491 7.39648 55.8192 7.39634Z" fill="#FFF7EA"/>
    <path d="M83.9571 5.04245C85.5472 5.04251 86.8836 5.47616 87.965 6.34434C89.0383 7.20455 89.6341 8.35234 89.7535 9.78592H86.7244C86.5813 9.06918 86.2587 8.52688 85.7578 8.16051C85.2649 7.79428 84.6484 7.61057 83.9092 7.61053C82.8598 7.61056 82.057 7.98224 81.5004 8.72277C80.9439 9.46346 80.6653 10.4868 80.6652 11.7929C80.6652 13.1308 80.9515 14.1583 81.5238 14.8752C82.0963 15.5921 82.8839 15.9507 83.8858 15.9507C84.6726 15.9505 85.3246 15.74 85.8413 15.3181C86.3578 14.8961 86.6603 14.3342 86.7478 13.6336H89.8014C89.682 15.0989 89.0774 16.2823 87.9884 17.1823C86.923 18.0583 85.5629 18.4964 83.9092 18.4964C81.977 18.4964 80.4382 17.8867 79.2932 16.668C78.1563 15.4573 77.5883 13.8243 77.5883 11.7694C77.5883 9.75431 78.1648 8.13323 79.3177 6.9066C80.4627 5.66418 82.0091 5.04245 83.9571 5.04245Z" fill="#FFF7EA"/>
    <path fill-rule="evenodd" clip-rule="evenodd" d="M96.9887 5.04245C98.8252 5.04254 100.269 5.58036 101.318 6.65559C102.392 7.70688 102.929 9.12874 102.929 10.9205C102.929 11.3824 102.885 11.8968 102.797 12.4622H93.7681C93.7601 13.6331 94.0388 14.537 94.6033 15.1742C95.1758 15.8114 95.9785 16.1302 97.0121 16.1303C97.7673 16.1302 98.4158 15.9552 98.9564 15.6048C99.4969 15.2544 99.8433 14.8001 99.9943 14.2427H103C102.785 15.501 102.125 16.5286 101.02 17.3251C99.9546 18.1056 98.6266 18.4963 97.0366 18.4964C95.1362 18.4964 93.6135 17.8866 92.4685 16.668C91.3156 15.4413 90.739 13.8161 90.739 11.7929C90.7391 9.74615 91.3072 8.11723 92.444 6.9066C93.589 5.66418 95.1043 5.04245 96.9887 5.04245ZM96.9174 7.39634C96.0031 7.39634 95.2625 7.67134 94.698 8.22076C94.1338 8.76223 93.8313 9.4832 93.7915 10.3828H99.8986C99.8825 9.4354 99.6123 8.70267 99.0878 8.18506C98.5711 7.65952 97.8473 7.39648 96.9174 7.39634Z" fill="#FFF7EA"/>
    <path d="M3.74734 18.2577H0.574629V0.418345H3.74734V18.2577Z" fill="#FFF7EA"/>
    <path d="M14.4648 5.04245C15.9037 5.04253 17.0089 5.5204 17.7801 6.47598C18.5513 7.42382 18.9371 8.73471 18.9371 10.4073V18.2577H15.8836V10.7174C15.8834 8.6469 14.9971 7.61063 13.2243 7.61053C12.2384 7.61053 11.4475 7.96612 10.8511 8.6748C10.2628 9.3757 9.9681 10.2959 9.96803 11.4348V18.2577H6.95011V5.29346H9.61056L9.96803 7.68304H10.1707C10.5762 6.83894 11.1529 6.18919 11.9002 5.73523C12.6475 5.27337 13.5028 5.04245 14.4648 5.04245Z" fill="#FFF7EA"/>
    <path d="M26.6133 0C27.2335 1.5991e-05 27.7865 0.0601402 28.2715 0.179609V2.61605C27.8584 2.53646 27.4925 2.49674 27.1746 2.49668C25.9343 2.49668 25.3139 3.09485 25.3137 4.28943V5.29346H28.2715V7.75444H25.3137V18.2577H22.2836V7.75444H19.8982V5.29346H22.2836V4.03842C22.2837 2.72432 22.6619 1.7247 23.4172 1.03973C24.1726 0.346953 25.238 3.51439e-05 26.6133 0Z" fill="#FFF7EA"/>
    <path d="M33.3697 18.2577H30.3763V0.179609H33.3697V18.2577Z" fill="#FFF7EA"/>
    <path d="M71.3553 5.04245C72.7942 5.0426 73.8994 5.52038 74.6706 6.47598C75.4418 7.42382 75.8276 8.73471 75.8276 10.4073V18.2577H72.7741V10.7174C72.7739 8.64701 71.8874 7.61074 70.1147 7.61053C69.1289 7.61053 68.3368 7.96609 67.7405 8.6748C67.1524 9.37565 66.8586 10.2961 66.8585 11.4348V18.2577H63.8406V5.29346H66.4999L66.8585 7.68304H67.0612C67.4667 6.83889 68.0433 6.1892 68.7906 5.73523C69.538 5.2734 70.3934 5.04245 71.3553 5.04245Z" fill="#FFF7EA"/>
  </svg>
</template>
<script setup>
defineProps({
  width: String,
  height: String
});
</script>