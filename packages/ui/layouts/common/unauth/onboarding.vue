<template>
  <template v-if="isMobile">
    <SplashScreen v-show="showSplash" />  
    <MobileRoleChosing  v-show="showChoseRole"/>
    <MobileCarousel v-show="showCarousel"/>
    <MobileLoginForm v-show="showLogin" />
  </template>
  <template v-else>
    <DesktopRoleChosing v-show="showChoseRole"/>
    <DesktopCarousel v-show="showCarousel"/>
    <DesktopLoginForm v-show="showLogin" />
  </template> 
</template>
<script setup lang="ts">
/*
  as semantycally "onboarding" is rather a page, then layout- it would better to keep <Page></Page> structure in template section here,
  but as mobile and desctop layout of Carousel and RoleChosing layouts have completely different structure- them have own <Page> skeletons
  that's why this module is placed to layouts, not to pages
*/
  import { computed, onMounted, ref } from 'vue';
  
  import { useUserStore } from '@stores/user.ts';
  import { useAppStore } from '@stores/app.ts';

  import { PAGES, DEVICE } from '@constants/core';
  import { CONFIGS } from '@constants/core/configs';

  import { SplashScreen } from '@ui/pages/mobile/unauth';

  import { RoleChosing as MobileRoleChosing } from '@ui/pages/mobile/unauth';
  import { RoleChosing as DesktopRoleChosing } from '@ui/pages/desktop/unauth';

  import { Carousel as MobileCarousel } from '@ui/pages/mobile/unauth';
  import { Carousel as DesktopCarousel } from '@ui/pages/desktop/unauth';

  import { LoginForm as MobileLoginForm } from '@ui/pages/mobile/unauth';
  import { LoginForm as DesktopLoginForm } from '@ui/pages/desktop/unauth';

  const userStore = useUserStore();
  const appStore = useAppStore();

  const showSplash = computed(() => appStore.page === PAGES.SPLASH);
  const showChoseRole = computed(() => appStore.page === PAGES.CHOSEROLE);
  const showCarousel = computed(() => appStore.page === PAGES.CAROUSEL);
  const showLogin = computed(() => appStore.page === PAGES.LOGINFORM);
  const isMobile = ref(appStore.device === DEVICE.MOBILE);
  
  onMounted(() => {
      if(isMobile.value) {
        appStore.setPage(PAGES.SPLASH);
        setTimeout(() => {
          appStore.setPage(PAGES.CHOSEROLE);
        }, CONFIGS.ONBOARDING.LOGO_TIMEOUT);
      } else {
        appStore.setPage(PAGES.CHOSEROLE);
      }
  });
</script>
<style scoped>

</style>