<template>
  <template v-if="isLoaded">
    <Dashboard v-if="authStore.isAuthenticated" />
    <Onboarding v-else />
  </template>
  <template v-else>
    <Loader />
  </template>   
</template>

<script setup lang="ts">
  import { ref, onMounted } from 'vue';
  import { useAuthStore } from '@stores/auth.ts';

  import { Dashboard } from '@ui/pages/common'
  import { Onboarding } from '@ui/layouts/common/unauth'
  import { Loader } from '@ui/pages/common'
  
  const isLoaded = ref(false);
  const authStore = useAuthStore();
  
  onMounted(async () => {
    await authStore.checkAuth();
    isLoaded.value = true;
  });
</script>
