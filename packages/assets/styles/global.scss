
.v-main {
  overflow-x:hidden!important;
}

.v-footer {
  background: none!important;
  flex: 0 0 auto!important;
  padding-bottom:30px!important;
  flex-direction: row;
  display:flex;
}

.h-screen-dvh {
  height: 100dvh;
}

body {
  font-family: 'Inter', sans-serif;
}

.btn{
  cursor:pointer;
  border-radius:20px;
  flex-shrink: 0;
}
.gradient-orange{
  background: linear-gradient(147deg, #FF1953 -2.43%, #FF9019 41.48%, #FFDDA2 97.54%);
}
.gradient-purple{
  background:  linear-gradient(147deg, #5F00E5 -2.43%, #8916FF 41.48%, #D392FF 97.54%);
}
.gradient-blue{
  background: linear-gradient(147deg, #7C16C9 -2.43%, #192CFF 41.48%, #6AD5FF 97.54%);
}
.cnt_container{
  padding-top:40px;
  padding-left:25px;
  padding-right:25px;
}

.blueback{
  background-image: url('../../assets/svg/blueback.svg'); 
}
.pinkback{
  background-image: url('../../assets/svg/bigPinkGradient.svg')!important; 
}
.influencer-icon{
  background-image: url('../../assets/svg/icons/influencer.svg'); 
}
.company-icon{
  background-image: url('../../assets/svg/icons/company.svg'); 
}
.asterisk-back{
  background-image: url('../../assets/svg/bigAsterisk.svg')!important; 
}