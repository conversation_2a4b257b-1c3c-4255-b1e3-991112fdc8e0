import { defineStore } from 'pinia';
import { DEVICE, PAGES } from '@constants/core'

interface AppState {
  device: DEVICE | null;
  page: PAGES | null;
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    device: null,
    page: null,
  }),
  actions: {
    setMobile() {
        this.device = DEVICE.MOBILE;
    },
    setTablet() {
        this.device = DEVICE.TABLET;
    },
    setDesktop() {
        this.device = DEVICE.DESKTOP;
    },
    setPage(page) {
        this.page = page;
    },
  },
});