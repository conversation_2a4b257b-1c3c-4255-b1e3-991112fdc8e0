import { defineStore } from 'pinia';
import axios from 'axios';

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isAuthenticated: false,
  }),
  actions: {
    async checkAuth() {
      try {
        const response = await axios.get('/api/check-auth');
        this.isAuthenticated = response.data.authenticated;
      } catch (error) {
        console.error('Auth check failed:', error);
        this.isAuthenticated = false;
      }
    },
    async login(credentials: { user: string; password: string }) {
      try {
        const response = await axios.post('/api/login', {
          method: 'POST',
          body: credentials,
        });
        if (response?.data.success) {
          this.isAuthenticated = true;
        } else {
          throw new Error('Invalid credentials');
        }
      } catch (error) {
        console.error('Login failed:', error);
        this.isAuthenticated = false;
      }
    },
    async logout() {
      try {
        await axios.get('/api/logout', { method: 'POST' });
        this.isAuthenticated = false;
      } catch (error) {
        console.error('Logout failed:', error);
      }
    },
  },
});