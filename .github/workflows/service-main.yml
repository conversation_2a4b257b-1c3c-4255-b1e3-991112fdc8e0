name: (main) build and deploy

on:
  workflow_dispatch:
  push:
    branches:
      - main

env:
  AWS_REGION: us-east-1
  APP_VERSION: ${{ github.run_number }}

jobs:
  build-and-push-docker:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.AWS_GITHUB_ROLE_ARN }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Docker Build and Push
        uses: Influence360/github-actions/docker-build-push-v1@v1.0.5
        with:
          aws_region: ${{ env.AWS_REGION }}
          aws_role: ${{ vars.AWS_GITHUB_ROLE_ARN }}
          aws_target_role: "arn:aws:iam::139561979141:role/github-actions/core-frontend-github-role"
          docker_context: .
          docker_push: true
          docker_image_tags: |
            type=raw,value=release-${{ github.run_number }}-${{ github.sha }}
          docker_ecr_repository_name: core-frontend
          docker_skip_if_image_exists: true

  deploy-staging:
    needs:
      - build-and-push-docker
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    environment:
      name: staging
    outputs:
      current_source_code_commit_sha: ${{ steps.update-service-version.outputs.current_source_code_commit_sha }}
      new_source_code_commit_sha: ${{ steps.update-service-version.outputs.new_source_code_commit_sha }}
    steps:
      - name: Update service version
        id: update-service-version
        uses: Influence360/github-actions/yaml-update-push-v1@v1.0.5
        with:
          changes: |
            {
              "services/core-frontend/service.yaml": {
                "version": "release-${{ github.run_number }}-${{ github.sha }}",
                "sourceCode.commitSha": "${{ github.sha }}",
                "sourceCode.github.actions.runNumber": ${{ github.run_number }}
              }
            }
          repository: Influence360/applications-staging
          labels: "Core Frontend, core-frontend, yaml-updates"
          message: "Update core-frontend versions to release-${{ github.run_number }}-${{ github.sha }}"
          token: ${{ secrets.GITOPS_APPLICATIONS_STAGING_GITHUB_PAT }}

      - name: Argo CD core-frontend sync
        uses: Influence360/github-actions/argo-cd-app-sync-v1@v1.0.5
        with:
          argo_cd_host: argo-cd-cli.staging.influence360.io
          argo_cd_token: ${{ secrets.ARGO_CD_CLI_STAGING_TOKEN }}
          app_name: core-frontend
          wait_health_status: true

  deploy-prod:
    needs:
      - build-and-push-docker
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    environment:
      name: prod
    outputs:
      current_source_code_commit_sha: ${{ steps.update-service-version.outputs.current_source_code_commit_sha }}
      new_source_code_commit_sha: ${{ steps.update-service-version.outputs.new_source_code_commit_sha }}
    steps:
      - name: Update service version
        id: update-service-version
        uses: Influence360/github-actions/yaml-update-push-v1@v1.0.5
        with:
          changes: |
            {
              "services/core-frontend/service.yaml": {
                "version": "release-${{ github.run_number }}-${{ github.sha }}",
                "sourceCode.commitSha": "${{ github.sha }}",
                "sourceCode.github.actions.runNumber": ${{ github.run_number }}
              }
            }
          repository: Influence360/applications-prod
          labels: "Core Frontend, core-frontend, yaml-updates"
          message: "Update core-frontend versions to release-${{ github.run_number }}-${{ github.sha }}"
          token: ${{ secrets.GITOPS_APPLICATIONS_PROD_GITHUB_PAT }}

      - name: Argo CD core-frontend sync
        uses: Influence360/github-actions/argo-cd-app-sync-v1@v1.0.5
        with:
          argo_cd_host: argo-cd-cli.prod.influence360.io
          argo_cd_token: ${{ secrets.ARGO_CD_CLI_PROD_TOKEN }}
          app_name: core-frontend
          wait_health_status: true
          sync_retry_limit: 0
