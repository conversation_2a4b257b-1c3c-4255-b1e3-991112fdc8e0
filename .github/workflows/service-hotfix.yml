name: (hotfix) build and update service version

on:
  workflow_dispatch:
  push:
    branches:
      - hotfix

env:
  AWS_REGION: us-east-1
  APP_VERSION: ${{ github.run_number }}

jobs:
  build-and-push-docker:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Docker Build and Push
        uses: Influence360/github-actions/docker-build-push-v1@v1.0.5
        with:
          aws_region: ${{ env.AWS_REGION }}
          aws_role: ${{ vars.AWS_GITHUB_ROLE_ARN }}
          aws_target_role: "arn:aws:iam::139561979141:role/github-actions/core-frontend-github-role"
          docker_context: .
          docker_push: true
          docker_image_tags: |
            type=raw,value=release-hotfix-${{ github.run_number }}-${{ github.sha }}
          docker_ecr_repository_name: core-frontend
          docker_skip_if_image_exists: true

  update-service-version-staging:
    needs:
      - build-and-push-docker
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Update service version
        id: update-service-version
        uses: Influence360/github-actions/yaml-update-pr-v1@v1.0.5
        with:
          changes: |
            {
              "services/core-frontend/service.yaml": {
                "version": "release-hotfix-${{ github.run_number }}-${{ github.sha }}",
                "sourceCode.commitSha": "${{ github.sha }}"
              }
            }
          repository: Influence360/applications-staging
          branch: "deployment/core-frontend-${{ github.sha }}"
          labels: "Core Frontend, core-frontend, yaml-updates"
          message: "Update core-frontend versions to release-hotfix-${{ github.run_number }}-${{ github.sha }}"
          token: ${{ secrets.GITOPS_APPLICATIONS_SANDBOXES_STAGING_GITHUB_PAT }}

  update-service-version-prod:
    needs:
      - build-and-push-docker
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: read
    steps:
      - name: Update service version
        id: update-service-version
        uses: Influence360/github-actions/yaml-update-pr-v1@v1.0.5
        with:
          changes: |
            {
              "services/core-frontend/service.yaml": {
                "version": "release-hotfix-${{ github.run_number }}-${{ github.sha }}",
                "sourceCode.commitSha": "${{ github.sha }}"
              }
            }
          repository: Influence360/applications-prod
          branch: "deployment/core-frontend-${{ github.sha }}"
          labels: "Core Frontend, core-frontend, yaml-updates"
          message: "Update core-frontend versions to release-hotfix-${{ github.run_number }}-${{ github.sha }}"
          token: ${{ secrets.GITOPS_APPLICATIONS_PROD_GITHUB_PAT }}
