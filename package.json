{"name": "core-frontend", "version": "1.0.0", "description": "", "keywords": [], "author": "Influence360", "private": true, "packageManager": "pnpm@10.12.1", "workspaces": ["packages/assets", "packages/constants", "packages/core", "packages/ui", "apps/mobile-native", "apps/web"], "scripts": {"build:web": "pnpm --filter web build", "build:mobile-native": "pnpm --filter mobile-native build", "build:all": "pnpm --filter web build && pnpm --filter mobile-native build", "preview:web": "pnpm --filter web dev", "preview:native": "pnpm --filter mobile-native dev"}, "license": "ISC", "dependencies": {"@nuxt/kit": "^3.17.5", "@nuxt/schema": "^3.17.5", "axios": "^1.9.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^4.1.8"}}