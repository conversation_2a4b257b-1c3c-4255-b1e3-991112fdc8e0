<template>
  <AppLayout />
</template>

<script setup lang="ts">

  import { AppLayout } from '@ui/layouts/common';
  import {useAppStore} from '@stores/app.ts';

  const { $device } = useNuxtApp();
  const appStore = useAppStore();
  if ($device.isMobile) {
    appStore.setMobile();
  } else if ($device.isTablet) {
    appStore.setTablet();
  } else if ($device.isDesktop) {
    appStore.setDesktop();
  }
</script>