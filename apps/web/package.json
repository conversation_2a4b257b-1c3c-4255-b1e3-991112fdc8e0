{"name": "web", "private": true, "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "start": "node .output/server/index.mjs", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --ext .ts,.vue", "format": "prettier --write ."}, "dependencies": {"assets": "file:../../packages/assets", "constants": "file:../../packages/constants", "core": "file:../../packages/core", "stores": "file:../../packages/stores", "ui": "file:../../packages/ui", "i18n": "file:../../packages/i18n", "@mdi/font": "^7.4.47", "@nuxtjs/device": "3.2.4", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "^9.5.5", "express-session": "^1.18.1", "nuxt": "^3.17.5", "vite-plugin-vuetify": "^2.1.1", "vue": "^3.5.16", "vue-i18n": "^10.0.7", "vue-router": "^4.5.1", "vuetify": "^3.8.8"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.8", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "sass-embedded": "^1.89.1", "tailwindcss": "^4.1.8"}}