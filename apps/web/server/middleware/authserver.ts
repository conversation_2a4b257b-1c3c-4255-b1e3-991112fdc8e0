import { defineEvent<PERSON><PERSON><PERSON>, readBody, eventHandler } from 'h3';
import session from 'express-session';

// Valid credentials
const validCredentials = {
  user: 'admin',
  password: 'admin',
};

// Configure express-session
const sessionMiddleware = session({
  secret: 'your-secret-key', // Replace with a strong random secret
  resave: false,
  saveUninitialized: false,
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // Secure cookies in production
    sameSite: 'strict',
  },
});

// Wrap middleware to work with h3
const useExpressMiddleware = (middleware) => {
  return eventHandler((event) => {
    return new Promise((resolve, reject) => {
      middleware(event.node.req, event.node.res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });
  });
};

// Use wrapped session middleware
const wrappedSessionMiddleware = useExpressMiddleware(sessionMiddleware);

export default defineEventHandler(async (event) => {
  // Execute session middleware for each request
  await wrappedSessionMiddleware(event);

  const url = event.node.req.url;

  // Login route
  if (url?.startsWith('/api/login')) {
    const body = await readBody(event);
    const { user, password } = body.body;

    if (user === validCredentials.user && password === validCredentials.password) {
      console.log('VALID CREDS')
      event.node.req.session.user = user; // Save user to session
      return { success: true };
    } else {
      console.log('INVALID CREDS')
      return { success: false, message: 'Invalid credentials' };
    }
  }

  // Auth check route
  if (url?.startsWith('/api/check-auth')) {
    if (event.node.req.session?.user) {
      return { authenticated: true };
    }
    return { authenticated: false };
  }

  // Logout route
  if (url?.startsWith('/api/logout')) {
    return new Promise((resolve, reject) => {
      if (event.node.req.session) {
        event.node.req.session.destroy((err) => {
          if (err) {
            reject({ success: false, message: 'Logout failed' });
          } else {
            resolve({ success: true });
          }
        });
      } else {
        resolve({ success: true });
      }
    });
  }
});