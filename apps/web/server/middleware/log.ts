import { defineEventHand<PERSON>, getHeader } from 'h3';
export default defineEventHandler((event) => {
  const userAgent = getHeader(event, 'user-agent') || '';
  const isMobile = /Mobi|Android|iPhone|iPad|iPod|Windows Phone/i.test(userAgent);
  const isTablet = /Tablet|iPad/i.test(userAgent) && !isMobile; 
  const isDesktop = !isMobile && !isTablet;


//  console.log('User-Agent:', userAgent);
//  console.log('Is Mobile:', isMobile);
//  console.log('Is isTablet:', isTablet);
 event.context.device = {
    isMobile,
    isTablet,
    isDesktop,
  };
})