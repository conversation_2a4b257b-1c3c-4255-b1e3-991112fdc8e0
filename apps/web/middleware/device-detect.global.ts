export default defineNuxtRouteMiddleware((to, from) => {
  // Access the `$device` helper from the Nuxt context
  const { $device } = useNuxtApp();

  // Log device information
  // console.log('Is Mobile:', $device.isMobile);
  // console.log('Is Tablet:', $device.isTablet);
  // console.log('Is Desktop:', $device.isDesktop);

  // Example: Redirect based on device type
  /* if ($device.isMobile && to.path !== '/mobile-only') {
    return navigateTo('/mobile-only');
  } */
});