import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
// import vueDevTools from 'vite-plugin-vue-devtools'
import path from 'path'



// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueJsx(),
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@ui': path.resolve(__dirname, '../../packages/ui'),
      '@core': path.resolve(__dirname, '../../packages/core'),
      '@assets': path.resolve(__dirname, '../../packages/assets'),
      '@constants': path.resolve(__dirname, '../../packages/constants'),
      '@i18n': path.resolve(__dirname, '../../packages/i18n'),
      '@stores': path.resolve(__dirname, '../../packages/stores'),
    },
  },
})
