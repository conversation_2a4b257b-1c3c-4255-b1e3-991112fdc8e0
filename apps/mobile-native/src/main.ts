import '@assets/styles/global.scss'
import '@assets/styles/tailwind.css'
import 'vuetify/styles'
import '@mdi/font/css/materialdesignicons.css'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { vuetifyConfigs } from '@constants/styling'


import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createVuetify } from 'vuetify';



import App from './App.vue'
import router from './router'
import { i18n4Vue } from '@i18n'
(async () => {
  const { default: axios } = await import('https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4');
  console.log(axios);
})();
const vuetify = createVuetify(Object.assign({}, vuetifyConfigs, {components, directives}));

const app = createApp(App);


app.use(createPinia())
app.use(i18n4Vue)
app.use(router)
app.use(vuetify)
app.mount('#app')
