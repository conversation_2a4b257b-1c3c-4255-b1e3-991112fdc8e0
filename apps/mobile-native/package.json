{"name": "mobile-native", "version": "0.0.0", "private": true, "type": "module", "packageManager": "pnpm@10.12.1", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lintts": "eslint . --ext .ts,.vue", "format": "prettier --write ."}, "dependencies": {"ui": "file:../../packages/ui", "core": "file:../../packages/core", "assets": "file:../../packages/assets", "constants": "file:../../packages/constants", "i18n": "file:../../packages/i18n", "stores": "file:../../packages/stores", "@capacitor/android": "^7.3.0", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/device": "^7.0.1", "@capacitor/ios": "^7.3.0", "@mdi/font": "^7.4.47", "pinia": "^3.0.3", "vue": "^3.5.16", "vue-router": "^4.5.1", "vuetify": "^3.8.8"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "eslint-plugin-vue": "~10.2.0", "jiti": "^2.4.2", "npm-run-all2": "^8.0.4", "typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-vue-devtools": "^7.7.6", "vite-plugin-vuetify": "^2.1.1", "vue-tsc": "^2.2.10"}}